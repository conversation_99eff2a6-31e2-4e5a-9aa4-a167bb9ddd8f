/* Import the gradient animation styles */
@import url('./styles/GradientAnimation.css');
/* Import performance optimization styles */
@import url('./styles/PerformanceOptimizations.css');

/* CSS Custom Properties for Theme Support */
:root {
  /* Dark theme colors (default) */
  --bg-primary: #101010;
  --bg-secondary: #1a1a1a;
  --bg-card: #1a1a1a;
  --bg-hover: #1e1e1e;
  --text-primary: #ffffff;
  --text-secondary: #a0aec0;
  --text-muted: #616161;
  --border-primary: rgba(255, 255, 255, 0.08);
  --border-hover: rgba(255, 255, 255, 0.12);
  --glass-bg: rgba(10, 10, 10, 0.7);
  --glass-border: rgba(255, 255, 255, 0.05);
  --navbar-bg: rgba(8, 8, 8, 0.9);
}

/* Light theme colors */
[data-theme="light"] {
  --bg-primary: #f7fafc;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;
  --bg-hover: #f7fafc;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-primary: rgba(0, 0, 0, 0.08);
  --border-hover: rgba(0, 0, 0, 0.12);
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.2);
  --navbar-bg: rgba(247, 250, 252, 0.9);
}

/* Global reset to eliminate black margins */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden;
  background-color: var(--bg-primary);
  transition: background-color 0.2s ease;
}

/* Allow auth page to override body background */
body.auth-page-no-scroll {
  background: linear-gradient(135deg,
    #0a1a15 0%,                     /* Very dark green tint */
    #0a1520 25%,                    /* Very dark blue tint */
    #0a1518 50%,                    /* Very dark mix */
    #0a1520 75%,                    /* Very dark blue tint */
    #0a1a15 100%                    /* Very dark green tint */
  ) !important;
}

#root {
  margin: 0 !important;
  padding: 0 !important;
  background-color: transparent;
}

.icon-container {
	border-radius: 50%;
	padding: 8px;
	width: 40px;
	height: 40px;
	transition: background-color 0.3s ease-in-out;
}

.icon-container:hover {
	background-color: var(--bg-hover);
}

/* Clean icon style without background */
.clean-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: transform 0.2s ease;
}

.clean-icon:hover {
	transform: scale(1.1);
}

/* Glass effect styles - optimized for performance */
.glass-card {
	background: var(--glass-bg) !important;
	backdrop-filter: blur(5px) !important; /* Reduced blur for better performance */
	border: 1px solid var(--glass-border) !important;
	box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2) !important; /* Reduced shadow */
	transition: none !important; /* Removed transition for better performance */
}

.glass-card:hover {
	border-color: var(--border-hover) !important; /* Subtle border change */
	box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2) !important; /* Consistent shadow */
}

/* Glass navbar - optimized for performance */
.glass-navbar {
	background: var(--navbar-bg) !important; /* Increased opacity to reduce need for blur */
	backdrop-filter: blur(4px) !important; /* Reduced blur for better performance */
	border-bottom: 1px solid var(--glass-border) !important;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important; /* Reduced shadow */
}

/* Glass tab - optimized for performance */
.glass-tab {
	background: var(--glass-bg) !important;
	backdrop-filter: blur(4px) !important; /* Reduced blur for better performance */
	border: 1px solid var(--glass-border) !important;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important; /* Reduced shadow */
	transition: none !important; /* Removed transition for better performance */
}

.glass-tab:hover {
	background: var(--bg-hover) !important; /* Subtle hover effect */
	border-color: var(--border-hover) !important;
}

/* Glass message bubble - light glassy effect for Telegram-like appearance */
.glass-message-bubble {
	backdrop-filter: blur(10px) saturate(180%) !important;
	-webkit-backdrop-filter: blur(10px) saturate(180%) !important;
	border: 1px solid rgba(255, 255, 255, 0.2) !important;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1),
	            0 1px 3px rgba(0, 0, 0, 0.08),
	            inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
	transition: all 0.2s ease !important;
}

.glass-message-bubble:hover {
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15),
	            0 2px 4px rgba(0, 0, 0, 0.1),
	            inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
	transform: translateY(-1px) !important;
}

/* Mobile optimizations for glass message bubbles */
@media (max-width: 768px) {
	.glass-message-bubble {
		backdrop-filter: blur(8px) saturate(150%) !important;
		-webkit-backdrop-filter: blur(8px) saturate(150%) !important;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08),
		            0 1px 2px rgba(0, 0, 0, 0.06),
		            inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
	}

	.glass-message-bubble:hover {
		transform: none !important; /* Disable hover transform on mobile */
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08),
		            0 1px 2px rgba(0, 0, 0, 0.06),
		            inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
	}
}

/* Active navigation indicator - simplified */
.nav-active-indicator {
	position: absolute;
	bottom: -2px;
	left: 50%;
	transform: translateX(-50%);
	width: 4px;
	height: 4px;
	border-radius: 50%;
	background-color: #00CC85;
	/* Removed box-shadow for better performance */
}

/* For mobile devices, position the indicator differently */
@media (max-width: 768px) {
	.nav-active-indicator {
		bottom: 0;
		width: 6px;
		height: 6px;
	}
}

.glass-tab[aria-selected="true"] {
	border-color: rgba(255, 255, 255, 0.1) !important;
	box-shadow: 0 4px 18px rgba(0, 0, 0, 0.25) !important;
}

/* Subtle gradient background */
.gradient-bg {
	background: linear-gradient(135deg, #080808 0%, #101010 100%);
	position: relative;
}

.gradient-bg::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(ellipse at top right, rgba(0, 204, 133, 0.1) 0%, rgba(0, 0, 0, 0) 50%);
	pointer-events: none;
}

.gradient-bg::after {
	content: '';
	position: absolute;
	bottom: 0;
	right: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(ellipse at bottom left, rgba(0, 121, 185, 0.1) 0%, rgba(0, 0, 0, 0) 50%);
	pointer-events: none;
}

/* Text wrapping for all content */
p, span, div, h1, h2, h3, h4, h5, h6, li, td, th, blockquote, pre, code {
	overflow-wrap: break-word;
	word-wrap: break-word;
	word-break: break-word;
	hyphens: auto;
}



/* Button hover effects */
.brand-button {
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.brand-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
	transition: all 0.5s ease;
}

.brand-button:hover::before {
	left: 100%;
}

/* Threads-like suggested user card - no hover effects */
.threads-user-card {
	background: var(--bg-primary) !important;
	border: 1px solid var(--border-primary) !important;
	transition: none !important;
}

.threads-user-card:hover {
	border-color: var(--border-primary) !important;
	box-shadow: none !important;
	transform: none !important;
}

/* Threads-like post card - rounded rectangle with visible styling */
.threads-post-card {
	background: var(--bg-card) !important;
	border: 1px solid var(--border-primary) !important;
	border-radius: 16px !important;
	transition: none !important;
	box-shadow: none !important;
}

.threads-post-card:hover {
	background: var(--bg-hover) !important;
	border: 1px solid var(--border-hover) !important;
	box-shadow: none !important;
	transform: none !important;
}

/* Accent colors for highlights */
.accent-primary {
	color: #00cc85 !important;
}

.accent-secondary {
	color: #0079b9 !important;
}

/* Transparent Slider Styles - optimized for performance */
.transparent-slider-container {
	background: var(--bg-secondary) !important; /* Increased opacity to reduce need for blur */
	backdrop-filter: blur(3px) !important; /* Reduced blur for better performance */
	border: 1px solid var(--glass-border) !important;
	box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15) !important; /* Reduced shadow */
	transition: none !important; /* Removed transition for better performance */
}

.transparent-slider-wrapper {
	overflow: visible !important;
}

/* Style for the suggested user cards - optimized for performance */
.suggested-user-card {
	background: var(--bg-secondary) !important;
	border: 1px solid var(--border-primary) !important;
	border-radius: 16px !important;
	transition: none !important; /* Removed transition for better performance */
}

.suggested-user-card:hover {
	border-color: var(--border-hover) !important;
	/* Removed transform for better performance */
}

/* Style for the search page suggested user list items */
.search-user-item {
	background: var(--bg-secondary) !important;
	border: none !important;
	border-radius: 16px !important;
	box-shadow: none !important;
	transition: none !important;
}

.search-user-item:hover {
	transform: none !important;
	border: none !important;
	box-shadow: none !important;
	background: var(--bg-secondary) !important;
}

/* Style for the slider navigation buttons */
.transparent-nav-button {
	background: rgba(30, 30, 30, 0.7) !important;
	backdrop-filter: blur(5px) !important;
	border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.transparent-nav-button:hover {
	background: rgba(40, 40, 40, 0.8) !important;
}

/* Override slick slider dots */
.transparent-slider .slick-dots li button:before {
	color: rgba(255, 255, 255, 0.5) !important;
}

.transparent-slider .slick-dots li.slick-active button:before {
	color: rgba(255, 255, 255, 0.8) !important;
}

/* Make the slider track transparent */
.transparent-slider .slick-track {
	display: flex !important;
	gap: 10px !important;
}

/* Message animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes messageNew {
	0% {
		opacity: 0;
		transform: translateY(10px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.message-new {
	animation: messageNew 0.3s ease-out forwards;
}

.message-item {
	position: relative;
	transition: all 0.2s ease;
}



.hover-effect {
	transition: background-color 0.2s ease;
}

/* Optimize images in messages */
.optimized-image {
	object-fit: contain;
	max-width: 100%;
	height: auto;
	will-change: transform;
}


/* ===== LEGACY RESPONSIVE RULES (for compatibility) ===== */

/* Responsive adjustments for small screens (800x900 and similar) */
@media (max-width: 900px) and (max-height: 1000px) {
	/* Ensure message input area has enough space above bottom navigation */
	.message-container {
		padding-bottom: 0px !important;
	}

	/* Adjust message input positioning for better visibility */
	.chakra-input {
		margin-bottom: 10px !important;
	}
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

/* Mobile logo scroll behavior - optimized for performance */
.mobile-logo-scroll {
	will-change: transform, opacity;
	backface-visibility: hidden;
	transform-style: preserve-3d;
}

/* Mobile-specific optimizations by screen size */
@media (max-width: 320px) {
	/* Ultra-compact mode for very small screens */
	.mobile-logo-scroll {
		transition: transform 0.2s ease, opacity 0.2s ease !important;
	}
}

@media (min-width: 321px) and (max-width: 767px) {
	/* Standard mobile optimizations */
	.mobile-logo-scroll {
		transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
		           opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
	}

	/* Optimize logo container for mobile scroll performance */
	.mobile-logo-scroll * {
		will-change: auto;
		transform: translateZ(0);
	}
}

/* ===== MOBILE TOUCH OPTIMIZATIONS ===== */

/* Improve touch targets for mobile */
@media (max-width: 767px) {
	/* Ensure minimum touch target size (44px) */
	button, .chakra-button {
		min-height: 44px !important;
		min-width: 44px !important;
	}

	/* Improve tap highlighting */
	button, .chakra-button, a {
		-webkit-tap-highlight-color: rgba(0, 204, 133, 0.2);
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
	}

	/* Prevent text selection on UI elements */
	.glass-navbar, .nav-active-indicator {
		-webkit-user-select: none;
		user-select: none;
	}
}

/* ===== MOBILE TYPOGRAPHY ===== */

/* Responsive font sizes for better mobile readability */
@media (max-width: 320px) {
	/* Very small screens - compact text */
	body, .chakra-text {
		font-size: 14px !important;
		line-height: 1.4 !important;
	}
}

@media (min-width: 321px) and (max-width: 374px) {
	/* Small mobile screens */
	body, .chakra-text {
		font-size: 15px !important;
		line-height: 1.45 !important;
	}
}

@media (min-width: 375px) and (max-width: 767px) {
	/* Standard mobile screens */
	body, .chakra-text {
		font-size: 16px !important;
		line-height: 1.5 !important;
	}
}









